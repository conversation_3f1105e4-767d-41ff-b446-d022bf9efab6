<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Individual Tax Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav>
        <a href="index.html">🏠 Dashboard</a>
        <a href="calculator.html">📊 Individual Tax Calculator</a>
        <a href="upload.html">📥 Bulk Tax Report</a>
    </nav>

    <h1>Individual Tax Calculator</h1>

    <form id="taxForm">
        <label for="taxYear">Select Tax Year:</label>
        <select id="taxYear" name="taxYear" required>
            <option value="">Choose a year</option>
            <option value="2025-2026">2025-2026</option>
            <option value="2024-2025">2024-2025</option>
            <option value="2023-2024">2023-2024</option>
            <option value="2022-2023">2022-2023</option>
            <option value="2021-2022">2021-2022</option>
            <option value="2020-2021">2020-2021</option>
            <option value="2019-2020">2019-2020</option>
            <option value="2018-2019">2018-2019</option>
            <option value="2017-2018">2017-2018</option>
            <option value="2016-2017">2016-2017</option>
            <option value="2015-2016">2015-2016</option>
            <option value="2014-2015">2014-2015</option>
        </select>

        <label for="monthlyIncome">Monthly Income:</label>
        <input type="number" id="monthlyIncome" name="monthlyIncome" placeholder="Enter your monthly income" required>

        <div class="button-container">
            <button type="submit">Calculate Tax</button>
        </div>
    </form>

    <div id="result" class="result" style="display: none;"></div>

    <table id="taxTable">
        <thead>
            <tr>
                <th>Tax Year</th>
                <th>Monthly Income Tax</th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>

    <footer>
        Developed by <a href="https://condevelopers.example" target="_blank">CON Developers</a>
    </footer>

    <script>
        document.getElementById("taxForm").addEventListener("submit", async function (event) {
            event.preventDefault();

            const taxYear = document.getElementById("taxYear").value;
            const monthlyIncome = document.getElementById("monthlyIncome").value;

            try {
                const response = await fetch("/calculate-tax", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({ taxYear, monthlyIncome })
                });

                const data = await response.json();

                if (response.ok) {
                    const resultDiv = document.getElementById("result");
                    resultDiv.innerHTML = `
                        <p><strong>Tax Year:</strong> ${data.mainResult.taxYear}</p>
                        <p><strong>Monthly Income:</strong> ${data.mainResult.monthlyIncome}</p>
                        <p><strong>Monthly Income Tax:</strong> ${data.mainResult.monthlyTax}</p>
                        <p><strong>Monthly Income After Tax:</strong> ${data.mainResult.monthlyIncomeAfterTax}</p>
                        <p><strong>Yearly Income:</strong> ${data.mainResult.yearlyIncome}</p>
                        <p><strong>Yearly Tax:</strong> ${data.mainResult.yearlyTax}</p>
                        <p><strong>Yearly Income After Tax:</strong> ${data.mainResult.yearlyIncomeAfterTax}</p>
                    `;
                    resultDiv.style.display = "block";

                    const taxTableBody = document.querySelector("#taxTable tbody");
                    taxTableBody.innerHTML = "";
                    data.allYearsTax.forEach(item => {
                        taxTableBody.innerHTML += `
                            <tr>
                                <td>${item.taxYear}</td>
                                <td>${item.monthlyTax}</td>
                            </tr>
                        `;
                    });
                } else {
                    alert(data.error);
                }
            } catch (error) {
                console.error("Error:", error);
                alert("An unexpected error occurred.");
            }
        });
    </script>
</body>
</html>
