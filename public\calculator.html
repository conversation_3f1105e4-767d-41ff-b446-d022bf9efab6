<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Individual Tax Calculator - Professional Tax Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="description" content="Calculate your income tax for different financial years with our professional tax calculator">
</head>
<body>
    <nav>
        <a href="index.html">
            <i class="fas fa-home"></i>
            Dashboard
        </a>
        <a href="calculator.html">
            <i class="fas fa-calculator"></i>
            Individual Calculator
        </a>
        <a href="upload.html">
            <i class="fas fa-upload"></i>
            Bulk Calculator
        </a>
    </nav>

    <div class="main-container">
        <h1>Individual Tax Calculator</h1>

        <div class="calculator-container">
            <!-- Left Column: Form -->
            <div class="form-container">
                <h2><i class="fas fa-calculator"></i> Calculate Your Tax</h2>
                <form id="taxForm">
                    <div class="form-group">
                        <label for="taxYear">
                            <i class="fas fa-calendar-alt"></i>
                            Tax Year
                        </label>
                        <select id="taxYear" name="taxYear" required>
                            <option value="">Choose a financial year</option>
                            <option value="2025-2026">2025-2026 (Latest)</option>
                            <option value="2024-2025">2024-2025</option>
                            <option value="2023-2024">2023-2024</option>
                            <option value="2022-2023">2022-2023</option>
                            <option value="2021-2022">2021-2022</option>
                            <option value="2020-2021">2020-2021</option>
                            <option value="2019-2020">2019-2020</option>
                            <option value="2018-2019">2018-2019</option>
                            <option value="2017-2018">2017-2018</option>
                            <option value="2016-2017">2016-2017</option>
                            <option value="2015-2016">2015-2016</option>
                            <option value="2014-2015">2014-2015</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="monthlyIncome">
                            <i class="fas fa-rupee-sign"></i>
                            Monthly Income
                        </label>
                        <input type="number"
                               id="monthlyIncome"
                               name="monthlyIncome"
                               placeholder="Enter your monthly income (₹)"
                               min="0"
                               step="1000"
                               required>
                    </div>

                    <button type="submit" id="calculateBtn">
                        <i class="fas fa-calculator"></i>
                        Calculate Tax
                    </button>
                </form>
            </div>

            <!-- Right Column: Results -->
            <div class="results-container">
                <!-- Main Result Card -->
                <div id="result" class="result">
                    <h3><i class="fas fa-chart-line"></i> Tax Calculation Results</h3>
                    <div class="result-grid" id="resultGrid">
                        <!-- Results will be populated here -->
                    </div>
                </div>

                <!-- Comparison Table -->
                <div class="table-container" id="comparisonTable" style="display: none;">
                    <div class="table-header">
                        <i class="fas fa-chart-bar"></i>
                        Tax Comparison Across Years
                    </div>
                    <div class="table-scroll">
                        <table id="taxTable">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-calendar"></i> Tax Year</th>
                                    <th><i class="fas fa-rupee-sign"></i> Monthly Tax</th>
                                    <th><i class="fas fa-percentage"></i> Effective Rate</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>Developed with ❤️ by <a href="https://condevelopers.example" target="_blank">CON Developers</a></p>
        <p class="text-secondary">Professional Tax Calculator for Indian Income Tax</p>
    </footer>

    <script>
        document.getElementById("taxForm").addEventListener("submit", async function (event) {
            event.preventDefault();

            const taxYear = document.getElementById("taxYear").value;
            const monthlyIncome = document.getElementById("monthlyIncome").value;
            const calculateBtn = document.getElementById("calculateBtn");
            const originalBtnText = calculateBtn.innerHTML;

            // Show loading state
            calculateBtn.innerHTML = '<span class="loading"></span> Calculating...';
            calculateBtn.disabled = true;

            try {
                const response = await fetch("/calculate-tax", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({ taxYear, monthlyIncome })
                });

                const data = await response.json();

                if (response.ok) {
                    // Show main results
                    displayMainResults(data.mainResult);

                    // Show comparison table
                    displayComparisonTable(data.allYearsTax, monthlyIncome);

                    // Show result sections with animation
                    document.getElementById("result").classList.add("show");
                    document.getElementById("comparisonTable").style.display = "block";

                    // Scroll to results on mobile
                    if (window.innerWidth <= 1024) {
                        document.getElementById("result").scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                } else {
                    showError(data.error);
                }
            } catch (error) {
                console.error("Error:", error);
                showError("An unexpected error occurred. Please try again.");
            } finally {
                // Reset button state
                calculateBtn.innerHTML = originalBtnText;
                calculateBtn.disabled = false;
            }
        });

        function displayMainResults(result) {
            const resultGrid = document.getElementById("resultGrid");
            const monthlyTaxRate = ((parseFloat(result.monthlyTax.replace(/,/g, '')) / parseFloat(result.monthlyIncome.replace(/,/g, ''))) * 100).toFixed(2);

            resultGrid.innerHTML = `
                <div class="result-item">
                    <div class="label">Tax Year</div>
                    <div class="value">${result.taxYear}</div>
                </div>
                <div class="result-item">
                    <div class="label">Monthly Income</div>
                    <div class="value">₹${result.monthlyIncome}</div>
                </div>
                <div class="result-item highlight">
                    <div class="label">Monthly Tax</div>
                    <div class="value">₹${result.monthlyTax}</div>
                </div>
                <div class="result-item">
                    <div class="label">Income After Tax</div>
                    <div class="value">₹${result.monthlyIncomeAfterTax}</div>
                </div>
                <div class="result-item">
                    <div class="label">Yearly Income</div>
                    <div class="value">₹${result.yearlyIncome}</div>
                </div>
                <div class="result-item highlight">
                    <div class="label">Yearly Tax</div>
                    <div class="value">₹${result.yearlyTax}</div>
                </div>
                <div class="result-item">
                    <div class="label">Yearly Income After Tax</div>
                    <div class="value">₹${result.yearlyIncomeAfterTax}</div>
                </div>
                <div class="result-item">
                    <div class="label">Effective Tax Rate</div>
                    <div class="value">${monthlyTaxRate}%</div>
                </div>
            `;
        }

        function displayComparisonTable(allYearsTax, monthlyIncome) {
            const taxTableBody = document.querySelector("#taxTable tbody");
            taxTableBody.innerHTML = "";

            // Sort by year (newest first)
            const sortedData = allYearsTax.sort((a, b) => b.taxYear.localeCompare(a.taxYear));

            sortedData.forEach(item => {
                const taxAmount = parseFloat(item.monthlyTax.replace(/,/g, ''));
                const effectiveRate = ((taxAmount / parseFloat(monthlyIncome)) * 100).toFixed(2);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>${item.taxYear}</strong></td>
                    <td class="tax-amount">₹${item.monthlyTax}</td>
                    <td>${effectiveRate}%</td>
                `;
                taxTableBody.appendChild(row);
            });
        }

        function showError(message) {
            const resultDiv = document.getElementById("result");
            resultDiv.innerHTML = `
                <div style="text-align: center; color: var(--danger-color);">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h3>Error</h3>
                    <p>${message}</p>
                </div>
            `;
            resultDiv.classList.add("show");
        }

        // Add input formatting for better UX
        document.getElementById("monthlyIncome").addEventListener("input", function(e) {
            let value = e.target.value.replace(/[^\d]/g, '');
            if (value) {
                // Add thousand separators for display
                e.target.value = parseInt(value).toLocaleString('en-IN');
            }
        });

        // Remove formatting before form submission
        document.getElementById("taxForm").addEventListener("submit", function(e) {
            const incomeInput = document.getElementById("monthlyIncome");
            incomeInput.value = incomeInput.value.replace(/,/g, '');
        });
    </script>
</body>
</html>
