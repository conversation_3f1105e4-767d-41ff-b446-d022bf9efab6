const express = require("express");
const cors = require("cors");
const multer = require("multer");
const xlsx = require("xlsx");
const path = require("path");
const fs = require("fs");

const app = express();
app.use(cors({ origin: "*" }));
app.use(express.json());
app.use(express.static(path.join(__dirname, "public")));

const upload = multer({ dest: "uploads/" });

// =================== Serve Frontend Pages ===================

// Dashboard
app.get("/", (req, res) => {
    res.sendFile(path.join(__dirname, "public", "index.html"));
});

// Individual Tax Calculator
app.get("/calculator", (req, res) => {
    res.sendFile(path.join(__dirname, "public", "calculator.html"));
});

// Bulk Tax Report Upload
app.get("/upload", (req, res) => {
    res.sendFile(path.join(__dirname, "public", "upload.html"));
});

// Bulk Tax Report Result Page
app.get("/report", (req, res) => {
    res.sendFile(path.join(__dirname, "public", "report.html"));
});

// =================== Helper Functions ===================

// Format numbers without decimals
function formatAccounting(number) {
    return number.toLocaleString("en-US", { maximumFractionDigits: 0 });
}

// Calculate monthly tax for all tax years
function calculateTaxForAllYears(monthlyIncome) {
    const taxFiles = fs.readdirSync(__dirname).filter((file) => file.endsWith(".js") && file !== "taxApi.js");
    const annualIncome = monthlyIncome * 12;

    return taxFiles.map((file) => {
        const taxYear = file.replace(".js", "");
        const taxCalculator = require(path.join(__dirname, file));
        const monthlyTax = taxCalculator(annualIncome);

        return {
            taxYear,
            monthlyTax: formatAccounting(monthlyTax),
        };
    });
}

// =================== API: Individual Tax Calculation ===================

app.post("/calculate-tax", async (req, res) => {
    console.log("Request received:", req.body);
    const { taxYear, monthlyIncome } = req.body;

    if (!taxYear || !monthlyIncome || isNaN(monthlyIncome)) {
        return res.status(400).json({ error: "Invalid input. Provide a valid taxYear and monthlyIncome." });
    }

    try {
        const taxCalculator = require(path.join(__dirname, `${taxYear}.js`));
        const annualIncome = monthlyIncome * 12;
        const monthlyTax = taxCalculator(annualIncome);
        const monthlyIncomeAfterTax = monthlyIncome - monthlyTax;
        const yearlyTax = monthlyTax * 12;
        const yearlyIncomeAfterTax = annualIncome - yearlyTax;

        const allYearsTax = calculateTaxForAllYears(monthlyIncome);

        res.json({
            mainResult: {
                taxYear,
                monthlyIncome: formatAccounting(monthlyIncome),
                monthlyTax: formatAccounting(monthlyTax),
                monthlyIncomeAfterTax: formatAccounting(monthlyIncomeAfterTax),
                yearlyIncome: formatAccounting(annualIncome),
                yearlyTax: formatAccounting(yearlyTax),
                yearlyIncomeAfterTax: formatAccounting(yearlyIncomeAfterTax),
            },
            allYearsTax,
        });
    } catch (err) {
        console.error("Error processing request:", err);
        res.status(500).json({ error: "Error calculating tax. Ensure the taxYear is supported." });
    }
});

// =================== API: Bulk Tax Report Upload ===================

app.post("/upload-tax-report", upload.single("file"), async (req, res) => {
    try {
        const { taxYear } = req.body;
        if (!taxYear) {
            return res.status(400).json({ error: "Tax year is required." });
        }

        const taxFilePath = path.join(__dirname, `${taxYear}.js`);
        if (!fs.existsSync(taxFilePath)) {
            return res.status(400).json({ error: "Invalid tax year." });
        }
        const taxCalculator = require(taxFilePath);

        const filePath = req.file.path;
        const workbook = xlsx.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const sheetData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

        if (sheetData.length === 0 || !sheetData[0].EmployeeName || !sheetData[0].Salary) {
            return res.status(400).json({ error: "Invalid Excel format. Ensure columns: Employee Name & Salary." });
        }

        const processedData = sheetData.map((row) => {
            const employeeName = row.EmployeeName;
            const salary = parseFloat(row.Salary);
            const annualIncome = salary * 12;
            const monthlyTax = taxCalculator(annualIncome);

            return {
                EmployeeName: employeeName,
                Salary: formatAccounting(salary),
                MonthlyTax: formatAccounting(monthlyTax),
            };
        });

        fs.unlinkSync(filePath);

        res.json({
            message: "Report generated successfully!",
            reportData: processedData,
            taxYear: taxYear
        });
    } catch (error) {
        console.error("Error processing file:", error);
        res.status(500).json({ error: "Error processing the file." });
    }
});

// =================== Start Server ===================

const PORT = 3020;
const HOST = "0.0.0.0";
app.listen(PORT, HOST, () => {
    console.log(`Tax Calculator App running at http://${HOST}:${PORT}`);
});
