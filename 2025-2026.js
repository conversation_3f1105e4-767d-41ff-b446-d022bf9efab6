function calculateTax2025_2026(annualIncome) {
    if (annualIncome > 10000000) {
        // Above 1 crore: 35% tax + 9% surcharge = 38.15% effective
        return (((616000 + (annualIncome - 4100000) * 0.35) * 1.09) / 12);
    } else if (annualIncome > 4100000) {
        // 41,00,001 to 1,00,00,000: 35% tax
        return ((616000 + (annualIncome - 4100000) * 0.35) / 12);
    } else if (annualIncome > 3200000) {
        // 32,00,001 to 41,00,000: 30% tax
        return ((346000 + (annualIncome - 3200000) * 0.30) / 12);
    } else if (annualIncome > 2200000) {
        // 22,00,001 to 32,00,000: 23% tax
        return ((116000 + (annualIncome - 2200000) * 0.23) / 12);
    } else if (annualIncome > 1200000) {
        // 12,00,001 to 22,00,000: 11% tax
        return ((6000 + (annualIncome - 1200000) * 0.11) / 12);
    } else if (annualIncome > 600000) {
        // 6,00,001 to 12,00,000: 1% tax
        return ((annualIncome - 600000) * 0.01 / 12);
    } else {
        // Up to 6,00,000: 0% tax
        return 0;
    }
}

module.exports = calculateTax2025_2026;
