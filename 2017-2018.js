function calculateTax2017_2018(annualIncome) {
    if (annualIncome <= 400000) {
        return 0;
    } else if (annualIncome <= 500000) {
        return (annualIncome - 400000) * 0.02 / 12;
    } else if (annualIncome <= 750000) {
        return (2000 + (annualIncome - 500000) * 0.05) / 12;
    } else if (annualIncome <= 1400000) {
        return (14500 + (annualIncome - 750000) * 0.1) / 12;
    } else if (annualIncome <= 1500000) {
        return (79500 + (annualIncome - 1400000) * 0.125) / 12;
    } else if (annualIncome <= 1800000) {
        return (92000 + (annualIncome - 1500000) * 0.15) / 12;
    } else if (annualIncome <= 2500000) {
        return (137000 + (annualIncome - 1800000) * 0.175) / 12;
    } else if (annualIncome <= 3000000) {
        return (259500 + (annualIncome - 2500000) * 0.2) / 12;
    } else if (annualIncome <= 3500000) {
        return (359500 + (annualIncome - 3000000) * 0.225) / 12;
    } else if (annualIncome <= 4000000) {
        return (472000 + (annualIncome - 3500000) * 0.25) / 12;
    } else if (annualIncome <= 7000000) {
        return (597000 + (annualIncome - 4000000) * 0.275) / 12;
    } else {
        return (1422000 + (annualIncome - 7000000) * 0.3) / 12;
    }
}

module.exports = calculateTax2017_2018;
