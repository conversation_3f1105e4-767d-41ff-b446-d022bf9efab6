function calculateTax2024_2025(annualIncome) {
    if (annualIncome > 4100000) {
        return (((annualIncome - 4100000) * 0.35) + 700000) / 12;
    } else if (annualIncome > 3200000) {
        return (((annualIncome - 3200000) * 0.3) + 430000) / 12;
    } else if (annualIncome > 2200000) {
        return (((annualIncome - 2200000) * 0.25) + 180000) / 12;
    } else if (annualIncome > 1200000) {
        return (((annualIncome - 1200000) * 0.15) + 30000) / 12;
    } else if (annualIncome > 600000) {
        return (annualIncome - 600000) * 0.05 / 12;
    } else {
        return 0;
    }
}

module.exports = calculateTax2024_2025;
