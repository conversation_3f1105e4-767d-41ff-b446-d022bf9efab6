function calculateTax2023_2024(annualIncome) {
    if (annualIncome > 6000000) {
        return (((annualIncome - 6000000) * 0.35) + 1095000) / 12;
    } else if (annualIncome > 3600000) {
        return (((annualIncome - 3600000) * 0.275) + 435000) / 12;
    } else if (annualIncome > 2400000) {
        return (((annualIncome - 2400000) * 0.225) + 165000) / 12;
    } else if (annualIncome > 1200000) {
        return (((annualIncome - 1200000) * 0.125) + 15000) / 12;
    } else if (annualIncome > 600000) {
        return (annualIncome - 600000) * 0.025 / 12;
    } else {
        return 0;
    }
}

module.exports = calculateTax2023_2024;
