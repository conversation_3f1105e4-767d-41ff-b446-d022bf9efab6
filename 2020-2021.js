function calculateTax2020_2021(annualIncome) {
    if (annualIncome <= 600000) {
        return 0;
    } else if (annualIncome <= 1200000) {
        return (annualIncome - 600000) * 0.05 / 12;
    } else if (annualIncome <= 1800000) {
        return (30000 + (annualIncome - 1200000) * 0.1) / 12;
    } else if (annualIncome <= 2500000) {
        return (90000 + (annualIncome - 1800000) * 0.15) / 12;
    } else if (annualIncome <= 3500000) {
        return (195000 + (annualIncome - 2500000) * 0.175) / 12;
    } else if (annualIncome <= 5000000) {
        return (370000 + (annualIncome - 3500000) * 0.2) / 12;
    } else if (annualIncome <= 8000000) {
        return (670000 + (annualIncome - 5000000) * 0.225) / 12;
    } else if (annualIncome <= 12000000) {
        return (1345000 + (annualIncome - 8000000) * 0.25) / 12;
    } else if (annualIncome <= 30000000) {
        return (2345000 + (annualIncome - 12000000) * 0.275) / 12;
    } else if (annualIncome <= 50000000) {
        return (7295000 + (annualIncome - 30000000) * 0.3) / 12;
    } else {
        return (13295000 + (annualIncome - 50000000) * 0.325) / 12;
    }
}

module.exports = calculateTax2020_2021;
