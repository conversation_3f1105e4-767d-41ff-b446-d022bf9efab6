<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tax Report</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <nav>
        <a href="index.html">🏠 Dashboard</a>
        <a href="calculator.html">📊 Individual Tax Calculator</a>
        <a href="upload.html">📥 Bulk Tax Report</a>
    </nav>

    <h1>Tax Report</h1>

    <div class="button-container">
        <button onclick="exportToExcel()">Export to Excel</button>
        <button onclick="exportToPDF()">Export to PDF</button>
        <button onclick="window.print()">Print Report</button>
    </div>

    <div id="reportContainer"></div>

    <footer>
        Developed by <a href="https://condevelopers.example" target="_blank">CON Developers</a>
    </footer>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const reportData = JSON.parse(sessionStorage.getItem("reportData"));
            const taxYear = sessionStorage.getItem("taxYear");

            if (!reportData) {
                document.getElementById("reportContainer").innerHTML = "<p>No report data available.</p>";
                return;
            }

            let tableHTML = `
                <h2>Tax Year: ${taxYear}</h2>
                <table id="reportTable">
                    <thead>
                        <tr>
                            <th>Employee Name</th>
                            <th>Salary</th>
                            <th>Monthly Income Tax</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            reportData.forEach(item => {
                tableHTML += `
                    <tr>
                        <td>${item.EmployeeName}</td>
                        <td>${item.Salary}</td>
                        <td><strong>${item.MonthlyTax}</strong></td>
                    </tr>
                `;
            });

            tableHTML += `</tbody></table>`;
            document.getElementById("reportContainer").innerHTML = tableHTML;
        });

        function exportToExcel() {
            const table = document.getElementById("reportTable");
            const workbook = XLSX.utils.table_to_book(table, { sheet: "Tax Report" });
            XLSX.writeFile(workbook, "Tax_Report.xlsx");
        }

        function exportToPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            doc.text("Tax Report", 14, 20);
            const table = document.getElementById("reportTable");

            const headers = Array.from(table.querySelectorAll("thead th")).map(th => th.innerText);
            const body = Array.from(table.querySelectorAll("tbody tr")).map(tr =>
                Array.from(tr.querySelectorAll("td")).map(td => td.innerText)
            );

            doc.autoTable({
                head: [headers],
                body: body,
                startY: 30,
                theme: 'grid'
            });

            doc.save("Tax_Report.pdf");
        }
    </script>
</body>
</html>
