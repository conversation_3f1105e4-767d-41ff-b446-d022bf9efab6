<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tax Report - Bulk Tax Calculation Results</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Print-specific styles -->
    <style>
        @media print {
            /* Hide everything except the printable content */
            body * {
                visibility: hidden;
            }

            .printable-content, .printable-content * {
                visibility: visible;
            }

            .printable-content {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                background: white !important;
                color: black !important;
                font-family: Arial, sans-serif;
                font-size: 12px;
                line-height: 1.4;
            }

            /* Hide action buttons in print */
            .action-buttons {
                display: none !important;
            }

            /* Print-specific table styling */
            .printable-content table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
                page-break-inside: auto;
            }

            .printable-content th,
            .printable-content td {
                border: 1px solid #000;
                padding: 8px;
                text-align: left;
                page-break-inside: avoid;
            }

            .printable-content th {
                background-color: #f0f0f0 !important;
                font-weight: bold;
            }

            .printable-content tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }

            /* Summary cards for print */
            .print-summary {
                display: flex;
                justify-content: space-around;
                margin-bottom: 20px;
                border: 1px solid #000;
                padding: 15px;
            }

            .print-summary-item {
                text-align: center;
                flex: 1;
            }

            .print-summary-item h4 {
                margin: 0 0 5px 0;
                font-size: 14px;
            }

            .print-summary-item p {
                margin: 0;
                font-size: 16px;
                font-weight: bold;
            }

            /* Page breaks */
            .page-break {
                page-break-before: always;
            }

            /* Header styling */
            .print-header {
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
            }

            .print-header h1 {
                margin: 0;
                font-size: 18px;
            }

            .print-header p {
                margin: 5px 0 0 0;
                font-size: 12px;
            }
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <nav>
        <a href="index.html">
            <i class="fas fa-home"></i>
            Dashboard
        </a>
        <a href="calculator.html">
            <i class="fas fa-calculator"></i>
            Individual Calculator
        </a>
        <a href="upload.html">
            <i class="fas fa-upload"></i>
            Bulk Calculator
        </a>
    </nav>

    <div class="main-container">
        <h1>Tax Calculation Report</h1>

        <!-- Report Header -->
        <div style="background: var(--surface-color); padding: 2rem; border-radius: var(--radius-xl); box-shadow: var(--shadow-lg); border: 1px solid var(--border-color); margin-bottom: 2rem;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                <div>
                    <h2 id="reportTitle" style="margin-bottom: 0.5rem; color: var(--primary-color);">
                        <i class="fas fa-file-alt"></i> Tax Report
                    </h2>
                    <p id="reportSubtitle" class="text-secondary">Generated on <span id="reportDate"></span></p>
                </div>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <button onclick="exportToExcel()" style="background: var(--secondary-color); padding: 0.75rem 1.5rem;">
                        <i class="fas fa-file-excel"></i>
                        Export Excel
                    </button>
                    <button onclick="exportToPDF()" style="background: var(--danger-color); padding: 0.75rem 1.5rem;">
                        <i class="fas fa-file-pdf"></i>
                        Export PDF
                    </button>
                    <button onclick="printReport()" style="background: var(--text-secondary); padding: 0.75rem 1.5rem;">
                        <i class="fas fa-print"></i>
                        Print
                    </button>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div id="summaryCards" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
            <!-- Summary cards will be populated by JavaScript -->
        </div>

        <!-- Report Table -->
        <div class="table-container">
            <div class="table-header">
                <i class="fas fa-users"></i>
                Employee Tax Details
            </div>
            <div class="table-scroll">
                <div id="reportContainer">
                    <!-- Report content will be populated here -->
                </div>
            </div>
        </div>

        <!-- Hidden printable content -->
        <div class="printable-content" style="display: none;">
            <div class="print-header">
                <h1 id="printTitle">Pakistan Income Tax Report</h1>
                <p id="printDate">Generated on: <span id="printReportDate"></span></p>
            </div>
            <div id="printSummary" class="print-summary">
                <!-- Summary will be populated by JavaScript -->
            </div>
            <div id="printTable">
                <!-- Table will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <footer>
        <p>Developed with ❤️ by <a href="https://condevelopers.example" target="_blank">CON Developers</a></p>
        <p class="text-secondary">Professional Tax Calculator for Pakistan Income Tax</p>
    </footer>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const reportData = JSON.parse(sessionStorage.getItem("reportData"));
            const taxYear = sessionStorage.getItem("taxYear");

            if (!reportData || !taxYear) {
                document.getElementById("reportContainer").innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem; color: var(--accent-color);"></i>
                        <h3>No Report Data Available</h3>
                        <p>Please upload a file first to generate a report.</p>
                        <button onclick="location.href='upload.html'" style="margin-top: 1rem;">
                            <i class="fas fa-upload"></i>
                            Go to Upload Page
                        </button>
                    </div>
                `;
                return;
            }

            // Set report date and title
            document.getElementById("reportDate").textContent = new Date().toLocaleDateString('en-IN');
            document.getElementById("reportTitle").innerHTML = `<i class="fas fa-file-alt"></i> Tax Report - ${taxYear}`;

            // Calculate summary statistics
            const totalEmployees = reportData.length;
            const totalSalary = reportData.reduce((sum, item) => {
                const salary = parseFloat(String(item.Salary).replace(/[₹,]/g, '')) || 0;
                return sum + salary;
            }, 0);
            const totalTax = reportData.reduce((sum, item) => {
                const tax = parseFloat(String(item.MonthlyTax).replace(/[₹,]/g, '')) || 0;
                return sum + tax;
            }, 0);
            const averageTax = totalEmployees > 0 ? totalTax / totalEmployees : 0;

            // Create summary cards
            const summaryCards = document.getElementById("summaryCards");
            summaryCards.innerHTML = `
                <div style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--radius-lg); box-shadow: var(--shadow-md); border: 1px solid var(--border-color); text-align: center;">
                    <div style="font-size: 2rem; color: var(--primary-color); margin-bottom: 0.5rem;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--text-primary);">${totalEmployees}</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Employees</div>
                </div>
                <div style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--radius-lg); box-shadow: var(--shadow-md); border: 1px solid var(--border-color); text-align: center;">
                    <div style="font-size: 2rem; color: var(--secondary-color); margin-bottom: 0.5rem;">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--text-primary);">${totalSalary.toLocaleString()}</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Monthly Salary</div>
                </div>
                <div style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--radius-lg); box-shadow: var(--shadow-md); border: 1px solid var(--border-color); text-align: center;">
                    <div style="font-size: 2rem; color: var(--accent-color); margin-bottom: 0.5rem;">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--text-primary);">${totalTax.toLocaleString()}</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Monthly Tax</div>
                </div>
                <div style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--radius-lg); box-shadow: var(--shadow-md); border: 1px solid var(--border-color); text-align: center;">
                    <div style="font-size: 2rem; color: var(--danger-color); margin-bottom: 0.5rem;">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--text-primary);">${totalSalary > 0 ? ((totalTax/totalSalary)*100).toFixed(3) : 0}%</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Average Tax Rate</div>
                </div>
            `;

            // Create table
            let tableHTML = `
                <table id="reportTable">
                    <thead>
                        <tr>
                            <th><i class="fas fa-user"></i> Employee Name</th>
                            <th><i class="fas fa-rupee-sign"></i> Monthly Salary</th>
                            <th><i class="fas fa-calculator"></i> Monthly Tax</th>
                            <th><i class="fas fa-percentage"></i> Tax Rate</th>
                            <th><i class="fas fa-money-bill-wave"></i> Net Salary</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            reportData.forEach(item => {
                const salary = parseFloat(String(item.Salary).replace(/[₹,]/g, '')) || 0;
                const tax = parseFloat(String(item.MonthlyTax).replace(/[₹,]/g, '')) || 0;
                const taxRate = salary > 0 ? ((tax / salary) * 100).toFixed(3) : '0.000';
                const netSalary = salary - tax;

                tableHTML += `
                    <tr>
                        <td><strong>${item.EmployeeName}</strong></td>
                        <td>${item.Salary}</td>
                        <td class="tax-amount">${item.MonthlyTax}</td>
                        <td>${taxRate}%</td>
                        <td>${netSalary.toLocaleString()}</td>
                    </tr>
                `;
            });

            tableHTML += `</tbody></table>`;
            document.getElementById("reportContainer").innerHTML = tableHTML;
        });

        function exportToExcel() {
            const reportData = JSON.parse(sessionStorage.getItem("reportData"));
            const taxYear = sessionStorage.getItem("taxYear");

            if (!reportData) return;

            // Prepare data for Excel
            const excelData = reportData.map(item => {
                const salary = parseFloat(String(item.Salary).replace(/[₹,]/g, '')) || 0;
                const tax = parseFloat(String(item.MonthlyTax).replace(/[₹,]/g, '')) || 0;
                const taxRate = salary > 0 ? ((tax / salary) * 100).toFixed(3) : '0.000';
                const netSalary = salary - tax;

                return {
                    'Employee Name': item.EmployeeName,
                    'Monthly Salary': salary,
                    'Monthly Tax': tax,
                    'Tax Rate (%)': taxRate,
                    'Net Salary': netSalary
                };
            });

            const worksheet = XLSX.utils.json_to_sheet(excelData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, `Tax Report ${taxYear}`);

            XLSX.writeFile(workbook, `Tax_Report_${taxYear}.xlsx`);
        }

        function exportToPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            const taxYear = sessionStorage.getItem("taxYear");
            const reportData = JSON.parse(sessionStorage.getItem("reportData"));

            if (!reportData) return;

            // Calculate summary statistics
            const totalEmployees = reportData.length;
            const totalSalary = reportData.reduce((sum, item) => {
                const salary = parseFloat(String(item.Salary).replace(/[₹,]/g, '')) || 0;
                return sum + salary;
            }, 0);
            const totalTax = reportData.reduce((sum, item) => {
                const tax = parseFloat(String(item.MonthlyTax).replace(/[₹,]/g, '')) || 0;
                return sum + tax;
            }, 0);
            const averageTaxRate = totalSalary > 0 ? ((totalTax/totalSalary)*100).toFixed(3) : 0;

            // Header with company branding
            doc.setFillColor(30, 64, 175); // Primary blue color
            doc.rect(0, 0, 210, 35, 'F');

            // Title
            doc.setTextColor(255, 255, 255);
            doc.setFontSize(24);
            doc.setFont('helvetica', 'bold');
            doc.text('PAKISTAN INCOME TAX REPORT', 105, 15, { align: 'center' });

            doc.setFontSize(14);
            doc.setFont('helvetica', 'normal');
            doc.text(`Financial Year: ${taxYear}`, 105, 25, { align: 'center' });

            // Reset text color
            doc.setTextColor(0, 0, 0);

            // Report metadata
            doc.setFontSize(10);
            doc.setFont('helvetica', 'normal');
            doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 45);
            doc.text(`Total Employees: ${totalEmployees}`, 14, 52);

            // Summary section with attractive boxes
            const summaryY = 60;
            doc.setFontSize(14);
            doc.setFont('helvetica', 'bold');
            doc.text('SUMMARY STATISTICS', 14, summaryY);

            // Summary boxes
            const boxWidth = 45;
            const boxHeight = 20;
            const startX = 14;
            const boxY = summaryY + 5;

            // Total Salary Box
            doc.setFillColor(240, 248, 255);
            doc.rect(startX, boxY, boxWidth, boxHeight, 'F');
            doc.setDrawColor(30, 64, 175);
            doc.rect(startX, boxY, boxWidth, boxHeight);
            doc.setFontSize(8);
            doc.setFont('helvetica', 'normal');
            doc.text('Total Monthly Salary', startX + 2, boxY + 6);
            doc.setFontSize(10);
            doc.setFont('helvetica', 'bold');
            doc.text(totalSalary.toLocaleString(), startX + 2, boxY + 14);

            // Total Tax Box
            doc.setFillColor(255, 240, 240);
            doc.rect(startX + boxWidth + 5, boxY, boxWidth, boxHeight, 'F');
            doc.setDrawColor(220, 38, 127);
            doc.rect(startX + boxWidth + 5, boxY, boxWidth, boxHeight);
            doc.setFontSize(8);
            doc.setFont('helvetica', 'normal');
            doc.text('Total Monthly Tax', startX + boxWidth + 7, boxY + 6);
            doc.setFontSize(10);
            doc.setFont('helvetica', 'bold');
            doc.text(totalTax.toLocaleString(), startX + boxWidth + 7, boxY + 14);

            // Average Tax Rate Box
            doc.setFillColor(240, 255, 240);
            doc.rect(startX + (boxWidth + 5) * 2, boxY, boxWidth, boxHeight, 'F');
            doc.setDrawColor(34, 197, 94);
            doc.rect(startX + (boxWidth + 5) * 2, boxY, boxWidth, boxHeight);
            doc.setFontSize(8);
            doc.setFont('helvetica', 'normal');
            doc.text('Average Tax Rate', startX + (boxWidth + 5) * 2 + 2, boxY + 6);
            doc.setFontSize(10);
            doc.setFont('helvetica', 'bold');
            doc.text(`${averageTaxRate}%`, startX + (boxWidth + 5) * 2 + 2, boxY + 14);

            // Employee details section
            const tableStartY = boxY + boxHeight + 15;
            doc.setFontSize(14);
            doc.setFont('helvetica', 'bold');
            doc.text('EMPLOYEE TAX DETAILS', 14, tableStartY);

            // Prepare table data
            const headers = ['Employee Name', 'Monthly Salary', 'Monthly Tax', 'Tax Rate (%)', 'Net Salary'];
            const body = reportData.map(item => {
                const salary = parseFloat(String(item.Salary).replace(/[₹,]/g, '')) || 0;
                const tax = parseFloat(String(item.MonthlyTax).replace(/[₹,]/g, '')) || 0;
                const taxRate = salary > 0 ? ((tax / salary) * 100).toFixed(3) : '0.000';
                const netSalary = salary - tax;

                return [
                    item.EmployeeName,
                    salary.toLocaleString(),
                    tax.toLocaleString(),
                    `${taxRate}%`,
                    netSalary.toLocaleString()
                ];
            });

            // Enhanced table styling
            doc.autoTable({
                head: [headers],
                body: body,
                startY: tableStartY + 8,
                theme: 'striped',
                styles: {
                    fontSize: 9,
                    cellPadding: 4,
                    lineColor: [200, 200, 200],
                    lineWidth: 0.1
                },
                headStyles: {
                    fillColor: [30, 64, 175],
                    textColor: [255, 255, 255],
                    fontSize: 10,
                    fontStyle: 'bold',
                    halign: 'center'
                },
                alternateRowStyles: {
                    fillColor: [248, 250, 252]
                },
                columnStyles: {
                    0: { halign: 'left', cellWidth: 40 },
                    1: { halign: 'right', cellWidth: 30 },
                    2: { halign: 'right', cellWidth: 30 },
                    3: { halign: 'center', cellWidth: 25 },
                    4: { halign: 'right', cellWidth: 30 }
                },
                margin: { left: 14, right: 14 },
                didDrawPage: function (data) {
                    // Footer
                    const pageCount = doc.internal.getNumberOfPages();
                    const pageSize = doc.internal.pageSize;
                    const pageHeight = pageSize.height ? pageSize.height : pageSize.getHeight();

                    doc.setFontSize(8);
                    doc.setTextColor(128, 128, 128);
                    doc.text(`Page ${data.pageNumber} of ${pageCount}`, 105, pageHeight - 10, { align: 'center' });
                    doc.text('Generated by Pakistan Income Tax Calculator', 105, pageHeight - 5, { align: 'center' });
                }
            });

            doc.save(`Pakistan_Tax_Report_${taxYear}.pdf`);
        }

        function printReport() {
            const reportData = JSON.parse(localStorage.getItem('reportData'));
            const taxYear = localStorage.getItem('taxYear');

            if (!reportData || !taxYear) {
                alert('No report data found. Please generate a report first.');
                return;
            }

            // Populate print header
            document.getElementById('printTitle').textContent = `Pakistan Income Tax Report - ${taxYear}`;
            document.getElementById('printReportDate').textContent = new Date().toLocaleDateString();

            // Calculate and populate summary
            const totalEmployees = reportData.length;
            const totalSalary = reportData.reduce((sum, item) => {
                const salary = parseFloat(String(item.Salary).replace(/[₹,]/g, '')) || 0;
                return sum + salary;
            }, 0);
            const totalTax = reportData.reduce((sum, item) => {
                const tax = parseFloat(String(item.MonthlyTax).replace(/[₹,]/g, '')) || 0;
                return sum + tax;
            }, 0);
            const averageTaxRate = totalSalary > 0 ? ((totalTax/totalSalary)*100).toFixed(3) : 0;

            document.getElementById('printSummary').innerHTML = `
                <div class="print-summary-item">
                    <h4>Total Employees</h4>
                    <p>${totalEmployees}</p>
                </div>
                <div class="print-summary-item">
                    <h4>Total Monthly Salary</h4>
                    <p>${totalSalary.toLocaleString()}</p>
                </div>
                <div class="print-summary-item">
                    <h4>Total Monthly Tax</h4>
                    <p>${totalTax.toLocaleString()}</p>
                </div>
                <div class="print-summary-item">
                    <h4>Average Tax Rate</h4>
                    <p>${averageTaxRate}%</p>
                </div>
            `;

            // Create print table
            let printTableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>Employee Name</th>
                            <th>Monthly Salary</th>
                            <th>Monthly Tax</th>
                            <th>Tax Rate (%)</th>
                            <th>Net Salary</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            reportData.forEach(item => {
                const salary = parseFloat(String(item.Salary).replace(/[₹,]/g, '')) || 0;
                const tax = parseFloat(String(item.MonthlyTax).replace(/[₹,]/g, '')) || 0;
                const taxRate = salary > 0 ? ((tax / salary) * 100).toFixed(3) : '0.000';
                const netSalary = salary - tax;

                printTableHTML += `
                    <tr>
                        <td>${item.EmployeeName}</td>
                        <td>${salary.toLocaleString()}</td>
                        <td>${tax.toLocaleString()}</td>
                        <td>${taxRate}%</td>
                        <td>${netSalary.toLocaleString()}</td>
                    </tr>
                `;
            });

            printTableHTML += `</tbody></table>`;
            document.getElementById('printTable').innerHTML = printTableHTML;

            // Show printable content and trigger print
            document.querySelector('.printable-content').style.display = 'block';
            window.print();
            document.querySelector('.printable-content').style.display = 'none';
        }
    </script>
</body>
</html>
