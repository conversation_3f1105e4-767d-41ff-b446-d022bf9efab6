<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tax Report - Bulk Tax Calculation Results</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <nav>
        <a href="index.html">
            <i class="fas fa-home"></i>
            Dashboard
        </a>
        <a href="calculator.html">
            <i class="fas fa-calculator"></i>
            Individual Calculator
        </a>
        <a href="upload.html">
            <i class="fas fa-upload"></i>
            Bulk Calculator
        </a>
    </nav>

    <div class="main-container">
        <h1>Tax Calculation Report</h1>

        <!-- Report Header -->
        <div style="background: var(--surface-color); padding: 2rem; border-radius: var(--radius-xl); box-shadow: var(--shadow-lg); border: 1px solid var(--border-color); margin-bottom: 2rem;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                <div>
                    <h2 id="reportTitle" style="margin-bottom: 0.5rem; color: var(--primary-color);">
                        <i class="fas fa-file-alt"></i> Tax Report
                    </h2>
                    <p id="reportSubtitle" class="text-secondary">Generated on <span id="reportDate"></span></p>
                </div>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <button onclick="exportToExcel()" style="background: var(--secondary-color); padding: 0.75rem 1.5rem;">
                        <i class="fas fa-file-excel"></i>
                        Export Excel
                    </button>
                    <button onclick="exportToPDF()" style="background: var(--danger-color); padding: 0.75rem 1.5rem;">
                        <i class="fas fa-file-pdf"></i>
                        Export PDF
                    </button>
                    <button onclick="window.print()" style="background: var(--text-secondary); padding: 0.75rem 1.5rem;">
                        <i class="fas fa-print"></i>
                        Print
                    </button>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div id="summaryCards" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
            <!-- Summary cards will be populated by JavaScript -->
        </div>

        <!-- Report Table -->
        <div class="table-container">
            <div class="table-header">
                <i class="fas fa-users"></i>
                Employee Tax Details
            </div>
            <div class="table-scroll">
                <div id="reportContainer">
                    <!-- Report content will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>Developed with ❤️ by <a href="https://condevelopers.example" target="_blank">CON Developers</a></p>
        <p class="text-secondary">Professional Tax Calculator for Pakistan Income Tax</p>
    </footer>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const reportData = JSON.parse(sessionStorage.getItem("reportData"));
            const taxYear = sessionStorage.getItem("taxYear");

            if (!reportData || !taxYear) {
                document.getElementById("reportContainer").innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem; color: var(--accent-color);"></i>
                        <h3>No Report Data Available</h3>
                        <p>Please upload a file first to generate a report.</p>
                        <button onclick="location.href='upload.html'" style="margin-top: 1rem;">
                            <i class="fas fa-upload"></i>
                            Go to Upload Page
                        </button>
                    </div>
                `;
                return;
            }

            // Set report date and title
            document.getElementById("reportDate").textContent = new Date().toLocaleDateString('en-IN');
            document.getElementById("reportTitle").innerHTML = `<i class="fas fa-file-alt"></i> Tax Report - ${taxYear}`;

            // Calculate summary statistics
            const totalEmployees = reportData.length;
            const totalSalary = reportData.reduce((sum, item) => sum + parseFloat(item.Salary.replace(/,/g, '')), 0);
            const totalTax = reportData.reduce((sum, item) => sum + parseFloat(item.MonthlyTax.replace(/,/g, '')), 0);
            const averageTax = totalTax / totalEmployees;

            // Create summary cards
            const summaryCards = document.getElementById("summaryCards");
            summaryCards.innerHTML = `
                <div style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--radius-lg); box-shadow: var(--shadow-md); border: 1px solid var(--border-color); text-align: center;">
                    <div style="font-size: 2rem; color: var(--primary-color); margin-bottom: 0.5rem;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--text-primary);">${totalEmployees}</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Employees</div>
                </div>
                <div style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--radius-lg); box-shadow: var(--shadow-md); border: 1px solid var(--border-color); text-align: center;">
                    <div style="font-size: 2rem; color: var(--secondary-color); margin-bottom: 0.5rem;">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--text-primary);">₹${totalSalary.toLocaleString('en-IN')}</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Monthly Salary</div>
                </div>
                <div style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--radius-lg); box-shadow: var(--shadow-md); border: 1px solid var(--border-color); text-align: center;">
                    <div style="font-size: 2rem; color: var(--accent-color); margin-bottom: 0.5rem;">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--text-primary);">₹${totalTax.toLocaleString('en-IN')}</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Monthly Tax</div>
                </div>
                <div style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--radius-lg); box-shadow: var(--shadow-md); border: 1px solid var(--border-color); text-align: center;">
                    <div style="font-size: 2rem; color: var(--danger-color); margin-bottom: 0.5rem;">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--text-primary);">${((totalTax/totalSalary)*100).toFixed(2)}%</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Average Tax Rate</div>
                </div>
            `;

            // Create table
            let tableHTML = `
                <table id="reportTable">
                    <thead>
                        <tr>
                            <th><i class="fas fa-user"></i> Employee Name</th>
                            <th><i class="fas fa-rupee-sign"></i> Monthly Salary</th>
                            <th><i class="fas fa-calculator"></i> Monthly Tax</th>
                            <th><i class="fas fa-percentage"></i> Tax Rate</th>
                            <th><i class="fas fa-money-bill-wave"></i> Net Salary</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            reportData.forEach(item => {
                const salary = parseFloat(item.Salary.replace(/,/g, ''));
                const tax = parseFloat(item.MonthlyTax.replace(/,/g, ''));
                const taxRate = ((tax / salary) * 100).toFixed(2);
                const netSalary = salary - tax;

                tableHTML += `
                    <tr>
                        <td><strong>${item.EmployeeName}</strong></td>
                        <td>${item.Salary}</td>
                        <td class="tax-amount">${item.MonthlyTax}</td>
                        <td>${taxRate}%</td>
                        <td>${netSalary.toLocaleString()}</td>
                    </tr>
                `;
            });

            tableHTML += `</tbody></table>`;
            document.getElementById("reportContainer").innerHTML = tableHTML;
        });

        function exportToExcel() {
            const reportData = JSON.parse(sessionStorage.getItem("reportData"));
            const taxYear = sessionStorage.getItem("taxYear");

            if (!reportData) return;

            // Prepare data for Excel
            const excelData = reportData.map(item => {
                const salary = parseFloat(item.Salary.replace(/,/g, ''));
                const tax = parseFloat(item.MonthlyTax.replace(/,/g, ''));
                const taxRate = ((tax / salary) * 100).toFixed(2);
                const netSalary = salary - tax;

                return {
                    'Employee Name': item.EmployeeName,
                    'Monthly Salary': salary,
                    'Monthly Tax': tax,
                    'Tax Rate (%)': taxRate,
                    'Net Salary': netSalary
                };
            });

            const worksheet = XLSX.utils.json_to_sheet(excelData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, `Tax Report ${taxYear}`);

            XLSX.writeFile(workbook, `Tax_Report_${taxYear}.xlsx`);
        }

        function exportToPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            const taxYear = sessionStorage.getItem("taxYear");
            const reportData = JSON.parse(sessionStorage.getItem("reportData"));

            if (!reportData) return;

            // Add title
            doc.setFontSize(20);
            doc.text(`Tax Report - ${taxYear}`, 14, 20);

            doc.setFontSize(12);
            doc.text(`Generated on: ${new Date().toLocaleDateString('en-IN')}`, 14, 30);

            // Prepare table data
            const headers = ['Employee Name', 'Monthly Salary', 'Monthly Tax', 'Tax Rate (%)', 'Net Salary'];
            const body = reportData.map(item => {
                const salary = parseFloat(item.Salary.replace(/,/g, ''));
                const tax = parseFloat(item.MonthlyTax.replace(/,/g, ''));
                const taxRate = ((tax / salary) * 100).toFixed(2);
                const netSalary = salary - tax;

                return [
                    item.EmployeeName,
                    `${salary.toLocaleString()}`,
                    `${tax.toLocaleString()}`,
                    `${taxRate}%`,
                    `${netSalary.toLocaleString()}`
                ];
            });

            doc.autoTable({
                head: [headers],
                body: body,
                startY: 40,
                theme: 'grid',
                styles: { fontSize: 8 },
                headStyles: { fillColor: [99, 102, 241] }
            });

            doc.save(`Tax_Report_${taxYear}.pdf`);
        }
    </script>
</body>
</html>
