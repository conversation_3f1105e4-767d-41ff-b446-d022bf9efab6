<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Tax Calculator - Upload Excel File</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="description" content="Upload Excel files with employee salary data and generate bulk tax reports">
</head>
<body>
    <nav>
        <a href="index.html">
            <i class="fas fa-home"></i>
            Dashboard
        </a>
        <a href="calculator.html">
            <i class="fas fa-calculator"></i>
            Individual Calculator
        </a>
        <a href="upload.html">
            <i class="fas fa-upload"></i>
            Bulk Calculator
        </a>
    </nav>

    <div class="main-container">
        <h1>Bulk Tax Calculator</h1>
        <p class="text-center text-secondary" style="font-size: 1.125rem; margin-bottom: 3rem;">
            Upload Excel files with employee data to generate comprehensive tax reports
        </p>

        <div style="max-width: 600px; margin: 0 auto;">
            <div class="form-container">
                <h2><i class="fas fa-file-upload"></i> Upload Employee Data</h2>

                <!-- Instructions -->
                <div style="background: linear-gradient(135deg, #f8fafc, #f1f5f9); padding: 1.5rem; border-radius: var(--radius-md); margin-bottom: 2rem; border: 1px solid var(--border-color);">
                    <h3 style="color: var(--primary-color); margin-bottom: 1rem; font-size: 1rem;">
                        <i class="fas fa-info-circle"></i> File Format Requirements
                    </h3>
                    <ul style="margin: 0; padding-left: 1.5rem; color: var(--text-secondary);">
                        <li>Excel file (.xlsx format)</li>
                        <li>Column 1: <strong>EmployeeName</strong></li>
                        <li>Column 2: <strong>Salary</strong> (monthly amount)</li>
                        <li>No header row required</li>
                    </ul>
                </div>

                <form id="uploadForm">
                    <div class="form-group">
                        <label for="taxYear">
                            <i class="fas fa-calendar-alt"></i>
                            Tax Year
                        </label>
                        <select id="taxYear" name="taxYear" required>
                            <option value="">Choose a financial year</option>
                            <option value="2025-2026">2025-2026 (Latest)</option>
                            <option value="2024-2025">2024-2025</option>
                            <option value="2023-2024">2023-2024</option>
                            <option value="2022-2023">2022-2023</option>
                            <option value="2021-2022">2021-2022</option>
                            <option value="2020-2021">2020-2021</option>
                            <option value="2019-2020">2019-2020</option>
                            <option value="2018-2019">2018-2019</option>
                            <option value="2017-2018">2017-2018</option>
                            <option value="2016-2017">2016-2017</option>
                            <option value="2015-2016">2015-2016</option>
                            <option value="2014-2015">2014-2015</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="file">
                            <i class="fas fa-file-excel"></i>
                            Excel File
                        </label>
                        <input type="file" id="file" name="file" accept=".xlsx,.xls" required>
                    </div>

                    <button type="submit" id="uploadBtn" style="margin-bottom: 1rem;">
                        <i class="fas fa-upload"></i>
                        Process File & Generate Report
                    </button>
                </form>
            </div>

            <!-- Sample File Download -->
            <div style="margin-top: 3rem; text-align: center; clear: both;">
                <div style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--radius-xl); box-shadow: var(--shadow-md); border: 1px solid var(--border-color);">
                    <h3 style="color: var(--text-primary); margin-bottom: 1rem;">
                        <i class="fas fa-download"></i> Need a Sample File?
                    </h3>
                    <p class="text-secondary" style="margin-bottom: 1rem;">
                        Download our sample Excel template to see the correct format
                    </p>
                    <button onclick="downloadSample()" style="background: var(--secondary-color); padding: 0.75rem 1.5rem; width: auto; display: inline-block;">
                        <i class="fas fa-download"></i>
                        Download Sample Excel
                    </button>
                </div>
            </div>

            <!-- Processing Status -->
            <div id="processingStatus" style="display: none; margin-top: 2rem;">
                <div style="background: var(--surface-color); padding: 2rem; border-radius: var(--radius-xl); box-shadow: var(--shadow-md); border: 1px solid var(--border-color); text-align: center;">
                    <div class="loading" style="margin-bottom: 1rem;"></div>
                    <h3 style="color: var(--primary-color);">Processing Your File...</h3>
                    <p class="text-secondary">Please wait while we calculate taxes for all employees</p>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>Developed with ❤️ by <a href="https://condevelopers.example" target="_blank">CON Developers</a></p>
        <p class="text-secondary">Professional Tax Calculator for Pakistan Income Tax</p>
    </footer>

    <script>
        document.getElementById("uploadForm").addEventListener("submit", async function (event) {
            event.preventDefault();

            const taxYear = document.getElementById("taxYear").value;
            const fileInput = document.getElementById("file");
            const uploadBtn = document.getElementById("uploadBtn");
            const processingStatus = document.getElementById("processingStatus");
            const originalBtnText = uploadBtn.innerHTML;

            if (!fileInput.files[0]) {
                alert("Please select a file to upload.");
                return;
            }

            // Show processing state
            uploadBtn.innerHTML = '<span class="loading"></span> Uploading...';
            uploadBtn.disabled = true;
            processingStatus.style.display = "block";

            const formData = new FormData();
            formData.append("taxYear", taxYear);
            formData.append("file", fileInput.files[0]);

            try {
                const response = await fetch("/upload-tax-report", {
                    method: "POST",
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    sessionStorage.setItem("reportData", JSON.stringify(data.reportData));
                    sessionStorage.setItem("taxYear", data.taxYear);
                    window.location.href = "report.html";
                } else {
                    alert(data.error || "An error occurred while processing the file.");
                }
            } catch (error) {
                console.error("Error:", error);
                alert("An unexpected error occurred. Please try again.");
            } finally {
                // Reset button state
                uploadBtn.innerHTML = originalBtnText;
                uploadBtn.disabled = false;
                processingStatus.style.display = "none";
            }
        });

        function downloadSample() {
            // Create sample data
            const sampleData = [
                ["EmployeeName", "Salary"],
                ["John Doe", "50000"],
                ["Jane Smith", "75000"],
                ["Mike Johnson", "100000"],
                ["Sarah Wilson", "60000"]
            ];

            // Convert to CSV format
            const csvContent = sampleData.map(row => row.join(",")).join("\n");

            // Create and download file
            const blob = new Blob([csvContent], { type: "text/csv" });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "sample_employee_data.csv";
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // File input validation
        document.getElementById("file").addEventListener("change", function(e) {
            const file = e.target.files[0];
            if (file) {
                const fileSize = file.size / 1024 / 1024; // Convert to MB
                if (fileSize > 10) {
                    alert("File size should be less than 10MB");
                    e.target.value = "";
                }
            }
        });
    </script>
</body>
</html>
