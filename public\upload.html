<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Tax Report Upload</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav>
        <a href="index.html">🏠 Dashboard</a>
        <a href="calculator.html">📊 Individual Tax Calculator</a>
        <a href="upload.html">📥 Bulk Tax Report</a>
    </nav>

    <h1>Bulk Tax Report Upload</h1>

    <form id="uploadForm">
        <label for="taxYear">Select Tax Year:</label>
        <select id="taxYear" name="taxYear" required>
            <option value="">Choose a year</option>
            <option value="2025-2026">2025-2026</option>
            <option value="2024-2025">2024-2025</option>
            <option value="2023-2024">2023-2024</option>
            <option value="2022-2023">2022-2023</option>
            <option value="2021-2022">2021-2022</option>
            <option value="2020-2021">2020-2021</option>
            <option value="2019-2020">2019-2020</option>
            <option value="2018-2019">2018-2019</option>
            <option value="2017-2018">2017-2018</option>
            <option value="2016-2017">2016-2017</option>
            <option value="2015-2016">2015-2016</option>
            <option value="2014-2015">2014-2015</option>
        </select>

        <label for="file">Upload Excel File:</label>
        <input type="file" id="file" name="file" accept=".xlsx" required>

        <div class="button-container">
            <button type="submit">Generate Report</button>
        </div>
    </form>

    <footer>
        Developed by <a href="https://condevelopers.example" target="_blank">CON Developers</a>
    </footer>

    <script>
        document.getElementById("uploadForm").addEventListener("submit", async function (event) {
            event.preventDefault();

            const formData = new FormData();
            formData.append("taxYear", document.getElementById("taxYear").value);
            formData.append("file", document.getElementById("file").files[0]);

            try {
                const response = await fetch("/upload-tax-report", {
                    method: "POST",
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    sessionStorage.setItem("reportData", JSON.stringify(data.reportData));
                    sessionStorage.setItem("taxYear", data.taxYear);
                    window.location.href = "report.html";
                } else {
                    alert(data.error);
                }
            } catch (error) {
                console.error("Error:", error);
                alert("An unexpected error occurred.");
            }
        });
    </script>
</body>
</html>
