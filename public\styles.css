/* General Styles */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #F4F4F9;
    color: #333;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    padding-bottom: 60px;
}

h1, h2, h3 {
    font-weight: bold;
    color: #2C3E50;
    text-align: center;
    margin: 20px 0;
}

/* Navigation Bar */
nav {
    background-color: #34495E;
    padding: 15px;
    display: flex;
    justify-content: center;
    gap: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s ease;
}

nav a:hover {
    color: #FF7B54;
}

/* Dashboard Cards */
.dashboard-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    padding: 20px;
}

.card {
    background-color: white;
    color: #2C3E50;
    padding: 25px;
    border-radius: 15px;
    width: 300px;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
}

/* Form Styles */
form {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    margin: 20px auto;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

select, input[type="number"], input[type="file"] {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 16px;
}

button {
    background-color: #FF7B54;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.3s ease;
    font-weight: bold;
}

button:hover {
    background-color: #FF4D4D;
    transform: scale(1.05);
}

/* Result Section */
.result, table {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    margin: 20px auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    border: 1px solid #ccc;
    text-align: center;
}

th {
    background-color: #34495E;
    color: white;
    font-weight: bold;
}

/* Footer */
footer {
    background: #2C3E50;
    color: white;
    padding: 15px;
    text-align: center;
    position: fixed;
    bottom: 0;
    width: 100%;
    box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

footer a {
    color: #FF7B54;
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #FFD700;
}

/* Button Container for Alignment */
.button-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    margin: 20px 0;
}

.button-container button {
    padding: 12px 20px;
    border-radius: 8px;
    border: none;
    background-color: #FF7B54;
    color: white;
    font-weight: bold;
    transition: background-color 0.3s ease, transform 0.3s ease;
    cursor: pointer;
}

.button-container button:hover {
    background-color: #FF4D4D;
    transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
        padding: 10px;
    }

    form, .result, table {
        width: 90%;
    }

    .button-container {
        flex-direction: column;
        gap: 10px;
    }
}
