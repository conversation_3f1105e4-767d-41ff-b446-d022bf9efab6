/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables for Theme */
:root {
    --primary-color: #1e40af;
    --primary-dark: #1e3a8a;
    --secondary-color: #059669;
    --accent-color: #dc2626;
    --danger-color: #dc2626;
    --background-color: #ffffff;
    --surface-color: #f8fafc;
    --text-primary: #111827;
    --text-secondary: #374151;
    --border-color: #d1d5db;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* General Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-color: var(--background-color);
    min-height: 100vh;
    color: var(--text-primary);
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

h1, h2, h3 {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 1rem 0;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: 2rem;
}

/* Navigation Bar */
nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    display: flex;
    justify-content: center;
    gap: 2rem;
    box-shadow: var(--shadow-md);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

nav a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

nav a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Main Container */
.main-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    min-height: calc(100vh - 80px);
}

/* Dashboard Cards */
.dashboard-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
}

.card {
    background: var(--surface-color);
    padding: 2rem;
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.card h2 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.card p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Calculator Layout */
.calculator-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    align-items: start;
}

/* Form Styles */
.form-container {
    background: var(--surface-color);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    position: sticky;
    top: 120px;
}

.form-container h2 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.5rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

select, input[type="number"], input[type="text"], input[type="file"] {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--surface-color);
    color: var(--text-primary);
}

select:focus, input[type="number"]:focus, input[type="text"]:focus, input[type="file"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.875rem 2rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 1rem;
    width: 100%;
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.2);
    transition: left 0.5s;
}

button:hover::before {
    left: 100%;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

button:active {
    transform: translateY(0);
}

/* Results Container */
.results-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Result Section */
.result {
    background: var(--surface-color);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    display: none;
}

.result.show {
    display: block;
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    text-align: center;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.result-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.result-item {
    text-align: center;
    padding: 1rem;
    background: var(--surface-color);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.result-item .label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.result-item .value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.result-item.highlight .value {
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* Table Styles */
.table-container {
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.table-header {
    background: var(--primary-color);
    color: white;
    padding: 1rem 1.5rem;
    font-weight: 600;
    font-size: 1.125rem;
}

table {
    width: 100%;
    border-collapse: collapse;
}

.table-scroll {
    max-height: 400px;
    overflow-y: auto;
}

th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--background-color);
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: sticky;
    top: 0;
    z-index: 10;
}

td {
    color: var(--text-primary);
    font-weight: 500;
}

tr:hover {
    background: var(--background-color);
}

.tax-amount {
    font-weight: 600;
    color: var(--secondary-color);
}

/* Footer */
footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    color: var(--text-secondary);
    padding: 1.5rem;
    text-align: center;
    border-top: 1px solid var(--border-color);
    margin-top: 3rem;
}

footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--primary-dark);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mt-2 { margin-top: 1rem; }

/* Responsive Design */
@media (max-width: 1024px) {
    .calculator-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .form-container {
        position: static;
    }
}

@media (max-width: 768px) {
    .main-container {
        padding: 1rem;
    }

    .dashboard-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    nav {
        padding: 1rem;
        gap: 1rem;
        flex-wrap: wrap;
    }

    nav a {
        font-size: 0.875rem;
        padding: 0.5rem;
    }

    h1 {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    .result-grid {
        grid-template-columns: 1fr;
    }

    .table-scroll {
        max-height: 300px;
    }

    th, td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .main-container {
        padding: 0.5rem;
    }

    .form-container, .result, .table-container {
        padding: 1rem;
    }

    h1 {
        font-size: 1.75rem;
    }

    .result-item {
        padding: 0.75rem;
    }

    .result-item .value {
        font-size: 1rem;
    }

    .result-item.highlight .value {
        font-size: 1.25rem;
    }
}
