function calculateTax2022_2023(annualIncome) {
    if (annualIncome > 12000000) {
        return (((annualIncome - 12000000) * 0.35) + 2955000) / 12;
    } else if (annualIncome > 6000000) {
        return (((annualIncome - 6000000) * 0.325) + 1005000) / 12;
    } else if (annualIncome > 3600000) {
        return (((annualIncome - 3600000) * 0.25) + 405000) / 12;
    } else if (annualIncome > 2400000) {
        return (((annualIncome - 2400000) * 0.2) + 165000) / 12;
    } else if (annualIncome > 1200000) {
        return (((annualIncome - 1200000) * 0.125) + 15000) / 12;
    } else if (annualIncome > 600000) {
        return (annualIncome - 600000) * 0.025 / 12;
    } else {
        return 0;
    }
}

module.exports = calculateTax2022_2023;
