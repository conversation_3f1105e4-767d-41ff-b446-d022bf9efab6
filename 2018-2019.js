function calculateTax2018_2019(annualIncome) {
    if (annualIncome <= 400000) {
        return 0;
    } else if (annualIncome <= 800000) {
        return 1000 / 12;
    } else if (annualIncome <= 1200000) {
        return 2000 / 12;
    } else if (annualIncome <= 2500000) {
        return Math.max((annualIncome - 1200000) * 0.05, 2000) / 12;
    } else if (annualIncome <= 4000000) {
        return (65000 + (annualIncome - 2500000) * 0.15) / 12;
    } else if (annualIncome <= 8000000) {
        return (290000 + (annualIncome - 4000000) * 0.2) / 12;
    } else {
        return (1090000 + (annualIncome - 8000000) * 0.25) / 12;
    }
}

module.exports = calculateTax2018_2019;
